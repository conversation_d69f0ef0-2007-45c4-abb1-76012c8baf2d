spring:
  redis:
    database: 4
    host: ***********
    port: 6379
    password: Y7dL9o#Mv!P2@8fJ
    timeout: 15000
    lettuce:
      pool:
        max-active: 100
        max-idle: 10
        min-idle: 1
        max-wait: 10000
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    username: gfin
    password: L3$7yX&f9z!Aq#4T
    url: **********************************************************************************************************************************************************************************************************************
    hikari:
      # 连接池最大连接数，默认是10
      maximumPoolSize: 20
      # 最小空闲连接数量
      minimumIdle: 2
      # 空闲连接存活最大时间，默认600000（10分钟）
      idleTimeout: 300000
      # 数据库连接超时时间,默认30秒，即30000
      connectionTimeout: 6000
      connectionTestQuery: SELECT 1
      ## 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      maxLifetime: 600000

jwt:
  secret: tCVjGIoWFU2YmYVc6Gg42I73WLTBL+8ikWm3Z/j8k5c=

rest:
  connectTimeout: 20000 # http连接超时时间，默认500ms
  readTimeout: 30000 # http读取超时时间，默认1000ms
  maxConnTotal: 200 # 表示连接池最大并发连接数,默认300
  maxConnPerRoute: 100 # 表示单路由的最大并发连接数,默认150
  validateAfterInactivity: 1000 # 空闲的永久连接检查间隔，默认2000ms

gfin:
  api:
    base-url: http://192.168.0.2:8090/

swift:
  bank-receivers:
    "@nmbank.ru": "NEWMRUMMXRLY"
    "@gazprombank.ru": "GAZPRUMMXRLY"
    "@bbr.ru": "BADJRUMMXRLY"
    "@bfi.com.cu": "BFICCUHHXRLY"
    "@163.com": "163COMHHXRLY"
    "@sina.cn": "163COMHHXRLY"
  launch-times:
    bbr: "2024-03-20T00:00:00"  # BBR bank launch time in ISO format